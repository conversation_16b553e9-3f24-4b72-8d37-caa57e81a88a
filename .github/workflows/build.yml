name: Unity Multi-Platform Build

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version number (e.g., 1.0.1)'
        required: false
        default: ''
      platforms:
        description: 'Platforms to build (comma-separated: windows,mac,linux,android,ios or all)'
        required: false
        default: 'all'
      development:
        description: 'Development build'
        required: false
        default: false
        type: boolean

env:
  UNITY_VERSION: 2022.3.8f1
  PROJECT_PATH: .

jobs:
  build:
    name: Build for ${{ matrix.targetPlatform }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - targetPlatform: StandaloneWindows64
            os: windows-latest
            buildMethod: BuildTools.CommandLineBuild.BuildWindows
          - targetPlatform: StandaloneOSX
            os: macos-latest
            buildMethod: BuildTools.CommandLineBuild.BuildMac
          - targetPlatform: StandaloneLinux64
            os: ubuntu-latest
            buildMethod: BuildTools.CommandLineBuild.BuildLinux
          - targetPlatform: Android
            os: ubuntu-latest
            buildMethod: BuildTools.CommandLineBuild.BuildAndroid
          - targetPlatform: iOS
            os: macos-latest
            buildMethod: BuildTools.CommandLineBuild.BuildiOS

    steps:
      # Checkout repository
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for git log
          lfs: true

      # Cache Unity Library
      - name: Cache Unity Library
        uses: actions/cache@v3
        with:
          path: Library
          key: Library-${{ matrix.targetPlatform }}-${{ hashFiles('Assets/**', 'Packages/**', 'ProjectSettings/**') }}
          restore-keys: |
            Library-${{ matrix.targetPlatform }}-
            Library-

      # Setup Unity
      - name: Setup Unity
        uses: game-ci/unity-builder@v4
        env:
          UNITY_LICENSE: ${{ secrets.UNITY_LICENSE }}
          UNITY_EMAIL: ${{ secrets.UNITY_EMAIL }}
          UNITY_PASSWORD: ${{ secrets.UNITY_PASSWORD }}
        with:
          projectPath: ${{ env.PROJECT_PATH }}
          unityVersion: ${{ env.UNITY_VERSION }}
          targetPlatform: ${{ matrix.targetPlatform }}
          buildMethod: ${{ matrix.buildMethod }}
          customParameters: |
            -version "${{ github.event.inputs.version || github.ref_name }}"
            ${{ github.event.inputs.development == 'true' && '-development' || '' }}

      # Upload Build Artifacts
      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v3
        with:
          name: Build-${{ matrix.targetPlatform }}
          path: |
            Builds/${{ matrix.targetPlatform }}/**/*
            !Builds/${{ matrix.targetPlatform }}/**/*.pdb
          retention-days: 30

      # Upload Build Logs
      - name: Upload Build Logs
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: BuildLogs-${{ matrix.targetPlatform }}
          path: |
            build.log
            *.log
          retention-days: 7

  # Create Release (only on tags)
  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: build
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download All Artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts

      - name: Create Release Archives
        run: |
          mkdir -p release
          cd artifacts
          
          # Create archives for each platform
          for dir in Build-*; do
            if [ -d "$dir" ]; then
              platform=$(echo $dir | sed 's/Build-//')
              echo "Creating archive for $platform"
              
              case $platform in
                "StandaloneWindows64")
                  cd "$dir" && zip -r "../../release/PerfumerProject-Windows-${{ github.ref_name }}.zip" . && cd ..
                  ;;
                "StandaloneOSX")
                  cd "$dir" && zip -r "../../release/PerfumerProject-Mac-${{ github.ref_name }}.zip" . && cd ..
                  ;;
                "StandaloneLinux64")
                  cd "$dir" && tar -czf "../../release/PerfumerProject-Linux-${{ github.ref_name }}.tar.gz" . && cd ..
                  ;;
                "Android")
                  cd "$dir" && cp *.apk "../../release/PerfumerProject-Android-${{ github.ref_name }}.apk" && cd ..
                  ;;
                "iOS")
                  cd "$dir" && zip -r "../../release/PerfumerProject-iOS-${{ github.ref_name }}.zip" . && cd ..
                  ;;
              esac
            fi
          done

      - name: Generate Release Notes
        id: release_notes
        run: |
          # Read version.json for changelog
          if [ -f "version.json" ]; then
            CHANGELOG=$(cat version.json | jq -r '.changelog // "No changelog available"')
          else
            CHANGELOG="No changelog available"
          fi
          
          # Create release notes
          cat > release_notes.md << EOF
          # Release ${{ github.ref_name }}
          
          ## Changes
          $CHANGELOG
          
          ## Build Information
          - **Build Date**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          - **Commit**: ${{ github.sha }}
          - **Unity Version**: ${{ env.UNITY_VERSION }}
          
          ## Downloads
          Choose the appropriate version for your platform:
          - **Windows**: PerfumerProject-Windows-${{ github.ref_name }}.zip
          - **Mac**: PerfumerProject-Mac-${{ github.ref_name }}.zip  
          - **Linux**: PerfumerProject-Linux-${{ github.ref_name }}.tar.gz
          - **Android**: PerfumerProject-Android-${{ github.ref_name }}.apk
          - **iOS**: PerfumerProject-iOS-${{ github.ref_name }}.zip (requires Xcode to build)
          EOF
          
          echo "release_notes<<EOF" >> $GITHUB_OUTPUT
          cat release_notes.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ github.ref_name }}
          name: PerfumerProject ${{ github.ref_name }}
          body: ${{ steps.release_notes.outputs.release_notes }}
          files: |
            release/*
          draft: false
          prerelease: ${{ contains(github.ref_name, 'alpha') || contains(github.ref_name, 'beta') || contains(github.ref_name, 'rc') }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Notification job
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [build]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.build.result == 'success'
        run: |
          echo "✅ Build completed successfully!"
          echo "All platforms built successfully for commit ${{ github.sha }}"
      
      - name: Notify Failure
        if: needs.build.result == 'failure'
        run: |
          echo "❌ Build failed!"
          echo "Build failed for commit ${{ github.sha }}"
          exit 1
