#!/bin/bash

# Unity Development Environment Setup Script for macOS
# Run with: chmod +x setup-unity-environment-mac.sh && ./setup-unity-environment-mac.sh

# Configuration
UNITY_VERSION="2022.3.8f1"
SKIP_UNITY_HUB=false
SKIP_XCODE=false
SKIP_ANDROID_SDK=false
SKIP_GIT=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-unity-hub)
            SKIP_UNITY_HUB=true
            shift
            ;;
        --skip-xcode)
            SKIP_XCODE=true
            shift
            ;;
        --skip-android-sdk)
            SKIP_ANDROID_SDK=true
            shift
            ;;
        --skip-git)
            SKIP_GIT=true
            shift
            ;;
        --unity-version)
            UNITY_VERSION="$2"
            shift 2
            ;;
        -h|--help)
            echo "Unity Development Environment Setup for macOS"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --skip-unity-hub     Skip Unity Hub installation"
            echo "  --skip-xcode         Skip Xcode installation"
            echo "  --skip-android-sdk   Skip Android SDK installation"
            echo "  --skip-git           Skip Git installation"
            echo "  --unity-version VER  Unity version to install (default: 2022.3.8f1)"
            echo "  -h, --help           Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}  Unity Development Environment Setup${NC}"
echo -e "${BLUE}  for macOS${NC}"
echo -e "${BLUE}=========================================${NC}"

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}This script is designed for macOS only!${NC}"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Homebrew
install_homebrew() {
    echo -e "${BLUE}Installing Homebrew package manager...${NC}"
    
    if command_exists brew; then
        echo -e "${GREEN}Homebrew is already installed.${NC}"
        return
    fi
    
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    echo -e "${GREEN}Homebrew installed successfully!${NC}"
}

# Function to install Git
install_git() {
    if [ "$SKIP_GIT" = true ]; then
        echo -e "${YELLOW}Skipping Git installation...${NC}"
        return
    fi
    
    echo -e "${BLUE}Installing Git...${NC}"
    
    if command_exists git; then
        echo -e "${GREEN}Git is already installed.${NC}"
        return
    fi
    
    brew install git
    echo -e "${GREEN}Git installed successfully!${NC}"
}

# Function to install Xcode Command Line Tools
install_xcode_tools() {
    if [ "$SKIP_XCODE" = true ]; then
        echo -e "${YELLOW}Skipping Xcode tools installation...${NC}"
        return
    fi
    
    echo -e "${BLUE}Installing Xcode Command Line Tools...${NC}"
    
    # Check if Xcode Command Line Tools are already installed
    if xcode-select -p &> /dev/null; then
        echo -e "${GREEN}Xcode Command Line Tools are already installed.${NC}"
    else
        xcode-select --install
        echo -e "${YELLOW}Please complete the Xcode Command Line Tools installation in the popup dialog.${NC}"
        echo -e "${YELLOW}Press any key after installation is complete...${NC}"
        read -n 1 -s
    fi
    
    # Suggest installing full Xcode for iOS development
    if [ ! -d "/Applications/Xcode.app" ]; then
        echo -e "${YELLOW}For iOS development, please install Xcode from the Mac App Store.${NC}"
        echo -e "${YELLOW}This is required for iOS builds.${NC}"
    else
        echo -e "${GREEN}Xcode is already installed.${NC}"
    fi
}

# Function to install Unity Hub
install_unity_hub() {
    if [ "$SKIP_UNITY_HUB" = true ]; then
        echo -e "${YELLOW}Skipping Unity Hub installation...${NC}"
        return
    fi
    
    echo -e "${BLUE}Installing Unity Hub...${NC}"
    
    # Check if Unity Hub is already installed
    if [ -d "/Applications/Unity Hub.app" ]; then
        echo -e "${GREEN}Unity Hub is already installed.${NC}"
        return
    fi
    
    # Download and install Unity Hub
    UNITY_HUB_URL="https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.dmg"
    TEMP_DMG="/tmp/UnityHub.dmg"
    
    echo -e "${BLUE}Downloading Unity Hub...${NC}"
    curl -L -o "$TEMP_DMG" "$UNITY_HUB_URL"
    
    # Mount the DMG
    echo -e "${BLUE}Installing Unity Hub...${NC}"
    hdiutil attach "$TEMP_DMG" -quiet
    
    # Copy Unity Hub to Applications
    cp -R "/Volumes/Unity Hub/Unity Hub.app" "/Applications/"
    
    # Unmount the DMG
    hdiutil detach "/Volumes/Unity Hub" -quiet
    
    # Clean up
    rm "$TEMP_DMG"
    
    echo -e "${GREEN}Unity Hub installed successfully!${NC}"
}

# Function to install Unity Editor
install_unity_editor() {
    echo -e "${BLUE}Installing Unity Editor $UNITY_VERSION...${NC}"
    
    # Check if Unity version is already installed
    UNITY_PATH="/Applications/Unity/Hub/Editor/$UNITY_VERSION/Unity.app"
    if [ -d "$UNITY_PATH" ]; then
        echo -e "${GREEN}Unity $UNITY_VERSION is already installed.${NC}"
        return
    fi
    
    # Install Unity using Unity Hub CLI (if available)
    UNITY_HUB_CLI="/Applications/Unity Hub.app/Contents/MacOS/Unity Hub"
    
    if [ -f "$UNITY_HUB_CLI" ]; then
        echo -e "${BLUE}Installing Unity $UNITY_VERSION with build modules...${NC}"
        
        # Install Unity with common modules
        "$UNITY_HUB_CLI" -- --headless install --version "$UNITY_VERSION" --module android,ios,mac-il2cpp,windows-mono,linux-il2cpp,webgl
        
        echo -e "${GREEN}Unity $UNITY_VERSION installed successfully!${NC}"
    else
        echo -e "${YELLOW}Unity Hub CLI not found. Please install Unity manually through Unity Hub.${NC}"
        echo -e "${YELLOW}1. Open Unity Hub${NC}"
        echo -e "${YELLOW}2. Go to Installs tab${NC}"
        echo -e "${YELLOW}3. Click 'Install Editor'${NC}"
        echo -e "${YELLOW}4. Select version $UNITY_VERSION${NC}"
        echo -e "${YELLOW}5. Add modules: Android, iOS, Mac, Windows, Linux, WebGL${NC}"
    fi
}

# Function to install Android SDK
install_android_sdk() {
    if [ "$SKIP_ANDROID_SDK" = true ]; then
        echo -e "${YELLOW}Skipping Android SDK installation...${NC}"
        return
    fi
    
    echo -e "${BLUE}Installing Android SDK...${NC}"
    
    # Check if Android SDK is already installed
    if [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME" ]; then
        echo -e "${GREEN}Android SDK is already installed.${NC}"
        return
    fi
    
    # Install Android SDK via Homebrew
    brew install --cask android-sdk
    
    # Set up environment variables
    ANDROID_SDK_PATH="$HOME/Library/Android/sdk"
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_PROFILE="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        SHELL_PROFILE="$HOME/.bash_profile"
    fi
    
    if [ -n "$SHELL_PROFILE" ]; then
        echo "" >> "$SHELL_PROFILE"
        echo "# Android SDK" >> "$SHELL_PROFILE"
        echo "export ANDROID_HOME=$ANDROID_SDK_PATH" >> "$SHELL_PROFILE"
        echo "export ANDROID_SDK_ROOT=$ANDROID_SDK_PATH" >> "$SHELL_PROFILE"
        echo "export PATH=\$PATH:\$ANDROID_HOME/platform-tools:\$ANDROID_HOME/tools" >> "$SHELL_PROFILE"
        
        # Source the profile
        source "$SHELL_PROFILE"
    fi
    
    echo -e "${GREEN}Android SDK installed and configured successfully!${NC}"
    echo -e "${YELLOW}Please restart your terminal or run 'source $SHELL_PROFILE' to load environment variables.${NC}"
}

# Function to install Node.js
install_nodejs() {
    echo -e "${BLUE}Installing Node.js...${NC}"
    
    if command_exists node; then
        echo -e "${GREEN}Node.js is already installed.${NC}"
        return
    fi
    
    brew install node
    echo -e "${GREEN}Node.js installed successfully!${NC}"
}

# Function to install additional development tools
install_dev_tools() {
    echo -e "${BLUE}Installing additional development tools...${NC}"
    
    # Install useful tools
    local tools=("wget" "jq" "tree")
    
    for tool in "${tools[@]}"; do
        if ! command_exists "$tool"; then
            echo -e "${BLUE}Installing $tool...${NC}"
            brew install "$tool"
        else
            echo -e "${GREEN}$tool is already installed.${NC}"
        fi
    done
}

# Function to create Unity project structure
create_unity_project_structure() {
    echo -e "${BLUE}Setting up Unity project structure...${NC}"
    
    local project_path=$(pwd)
    
    # Create necessary directories
    local directories=(
        "Assets/Editor"
        "Assets/Scripts"
        "Assets/Prefabs"
        "Assets/Scenes"
        "Assets/Materials"
        "Assets/Textures"
        "Assets/Audio"
        "Builds"
    )
    
    for dir in "${directories[@]}"; do
        local full_path="$project_path/$dir"
        if [ ! -d "$full_path" ]; then
            mkdir -p "$full_path"
            echo -e "${GREEN}Created directory: $dir${NC}"
        fi
    done
    
    echo -e "${GREEN}Unity project structure created successfully!${NC}"
}

# Function to show installation summary
show_installation_summary() {
    echo -e "${BLUE}\n=========================================${NC}"
    echo -e "${BLUE}    INSTALLATION SUMMARY${NC}"
    echo -e "${BLUE}=========================================${NC}"
    
    echo -e "${GREEN}\nInstalled Components:${NC}"
    
    if command_exists brew; then
        echo -e "${GREEN}✓ Homebrew Package Manager${NC}"
    fi
    
    if command_exists git; then
        echo -e "${GREEN}✓ Git Version Control${NC}"
    fi
    
    if xcode-select -p &> /dev/null; then
        echo -e "${GREEN}✓ Xcode Command Line Tools${NC}"
    fi
    
    if [ -d "/Applications/Xcode.app" ]; then
        echo -e "${GREEN}✓ Xcode IDE${NC}"
    fi
    
    if [ -d "/Applications/Unity Hub.app" ]; then
        echo -e "${GREEN}✓ Unity Hub${NC}"
    fi
    
    if [ -d "/Applications/Unity/Hub/Editor/$UNITY_VERSION/Unity.app" ]; then
        echo -e "${GREEN}✓ Unity Editor $UNITY_VERSION${NC}"
    fi
    
    if command_exists node; then
        echo -e "${GREEN}✓ Node.js${NC}"
    fi
    
    if [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME" ]; then
        echo -e "${GREEN}✓ Android SDK${NC}"
    fi
    
    echo -e "${YELLOW}\nNext Steps:${NC}"
    echo -e "${YELLOW}1. Restart your terminal to load environment variables${NC}"
    echo -e "${YELLOW}2. Open Unity Hub and sign in with your Unity account${NC}"
    echo -e "${YELLOW}3. Create a new Unity project or open an existing one${NC}"
    echo -e "${YELLOW}4. For iOS development, make sure Xcode is installed from Mac App Store${NC}"
    echo -e "${YELLOW}5. Run the build system setup: Build Tools > Setup Build System${NC}"
    
    echo -e "${BLUE}\n=========================================${NC}"
}

# Main installation process
main() {
    echo -e "${GREEN}Starting Unity development environment setup for macOS...${NC}"
    echo -e "${YELLOW}This may take 30-60 minutes depending on your internet connection.${NC}"
    
    read -p $'\nDo you want to continue? (y/n): ' -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Installation cancelled.${NC}"
        exit 0
    fi
    
    # Make script executable
    chmod +x "$0"
    
    # Install components
    install_homebrew
    install_git
    install_nodejs
    install_dev_tools
    install_xcode_tools
    install_unity_hub
    install_unity_editor
    install_android_sdk
    create_unity_project_structure
    
    show_installation_summary
    
    echo -e "${GREEN}\nInstallation completed successfully!${NC}"
    echo -e "${YELLOW}Please restart your terminal to ensure all environment variables are loaded.${NC}"
}

# Run main function
main "$@"
