@echo off
setlocal enabledelayedexpansion

:: Configuration
set UNITY_PATH="C:\Program Files\Unity\Hub\Editor\2022.3.8f1\Editor\Unity.exe"
set PROJECT_PATH=%~dp0
set LOG_FILE=%PROJECT_PATH%build.log

:: Colors for output
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

echo %BLUE%========================================%NC%
echo %BLUE%    Unity Multi-Platform Builder       %NC%
echo %BLUE%========================================%NC%
echo.

:: Check if Unity exists
if not exist %UNITY_PATH% (
    echo %RED%Error: Unity not found at %UNITY_PATH%%NC%
    echo Please update UNITY_PATH in this script
    pause
    exit /b 1
)

:: Parse command line arguments
set PLATFORM=all
set VERSION=
set DEVELOPMENT=false

:parse_args
if "%~1"=="" goto :done_parsing
if "%~1"=="-platform" (
    set PLATFORM=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-version" (
    set VERSION=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-development" (
    set DEVELOPMENT=true
    shift
    goto :parse_args
)
if "%~1"=="-help" (
    goto :show_help
)
shift
goto :parse_args

:done_parsing

:: Show current settings
echo %YELLOW%Build Configuration:%NC%
echo   Platform: %PLATFORM%
if not "%VERSION%"=="" (
    echo   Version: %VERSION%
)
echo   Development Build: %DEVELOPMENT%
echo   Project Path: %PROJECT_PATH%
echo   Log File: %LOG_FILE%
echo.

:: Confirm build
set /p CONFIRM="Continue with build? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Build cancelled.
    exit /b 0
)

echo %BLUE%Starting Unity build...%NC%
echo.

:: Prepare Unity command
set UNITY_CMD=%UNITY_PATH% -batchmode -quit -projectPath "%PROJECT_PATH%" -logFile "%LOG_FILE%"

:: Add method based on platform
if /i "%PLATFORM%"=="all" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildAll
) else if /i "%PLATFORM%"=="windows" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildWindows
) else if /i "%PLATFORM%"=="mac" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildMac
) else if /i "%PLATFORM%"=="linux" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildLinux
) else if /i "%PLATFORM%"=="android" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildAndroid
) else if /i "%PLATFORM%"=="ios" (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.BuildiOS
) else (
    set UNITY_CMD=!UNITY_CMD! -executeMethod BuildTools.CommandLineBuild.Build -platform %PLATFORM%
)

:: Add version if specified
if not "%VERSION%"=="" (
    set UNITY_CMD=!UNITY_CMD! -version "%VERSION%"
)

:: Add development flag if specified
if /i "%DEVELOPMENT%"=="true" (
    set UNITY_CMD=!UNITY_CMD! -development
)

:: Execute Unity build
echo %YELLOW%Executing: !UNITY_CMD!%NC%
echo.

!UNITY_CMD!

:: Check result
if %ERRORLEVEL% equ 0 (
    echo.
    echo %GREEN%========================================%NC%
    echo %GREEN%         BUILD SUCCESSFUL!             %NC%
    echo %GREEN%========================================%NC%
    echo.
    echo Build completed successfully for platform: %PLATFORM%
    if exist "%PROJECT_PATH%Builds" (
        echo Output directory: %PROJECT_PATH%Builds
        echo.
        echo %YELLOW%Build contents:%NC%
        dir /b "%PROJECT_PATH%Builds"
    )
) else (
    echo.
    echo %RED%========================================%NC%
    echo %RED%           BUILD FAILED!               %NC%
    echo %RED%========================================%NC%
    echo.
    echo Build failed with error code: %ERRORLEVEL%
    echo Check the log file for details: %LOG_FILE%
    echo.
    if exist "%LOG_FILE%" (
        echo %YELLOW%Last few lines of log:%NC%
        powershell "Get-Content '%LOG_FILE%' | Select-Object -Last 20"
    )
)

echo.
pause
exit /b %ERRORLEVEL%

:show_help
echo.
echo %BLUE%Unity Multi-Platform Builder Help%NC%
echo.
echo Usage: build.bat [options]
echo.
echo Options:
echo   -platform ^<platform^>   Platform to build (all, windows, mac, linux, android, ios)
echo   -version ^<version^>      Version number (e.g., 1.0.1)
echo   -development            Enable development build
echo   -help                   Show this help message
echo.
echo Examples:
echo   build.bat                                    # Build all platforms
echo   build.bat -platform windows                 # Build Windows only
echo   build.bat -platform android -version 1.0.1 # Build Android with version 1.0.1
echo   build.bat -platform all -development        # Build all platforms in development mode
echo.
pause
exit /b 0
