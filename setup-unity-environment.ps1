# Unity Development Environment Setup Script for Windows
# Run this script as Administrator in PowerShell

param(
    [switch]$SkipUnityHub,
    [switch]$SkipVisualStudio,
    [switch]$SkipGit,
    [switch]$SkipAndroidSDK,
    [string]$UnityVersion = "2022.3.8f1"
)

# Colors for output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Chocolatey {
    Write-ColorOutput $Blue "Installing Chocolatey package manager..."
    
    if (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "Chocolatey is already installed."
        return
    }
    
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Chocolatey installed successfully!"
}

function Install-Git {
    if ($SkipGit) {
        Write-ColorOutput $Yellow "Skipping Git installation..."
        return
    }
    
    Write-ColorOutput $Blue "Installing Git..."
    
    if (Get-Command git -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "Git is already installed."
        return
    }
    
    choco install git -y
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Git installed successfully!"
}

function Install-VisualStudio {
    if ($SkipVisualStudio) {
        Write-ColorOutput $Yellow "Skipping Visual Studio installation..."
        return
    }
    
    Write-ColorOutput $Blue "Installing Visual Studio Community 2022..."
    
    # Check if Visual Studio is already installed
    $vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe"
    if (Test-Path $vsPath) {
        Write-ColorOutput $Green "Visual Studio Community 2022 is already installed."
        return
    }
    
    # Download Visual Studio installer
    $installerUrl = "https://aka.ms/vs/17/release/vs_community.exe"
    $installerPath = "$env:TEMP\vs_community.exe"
    
    Write-ColorOutput $Blue "Downloading Visual Studio installer..."
    Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath
    
    # Install with Unity workload
    Write-ColorOutput $Blue "Installing Visual Studio with Unity workload..."
    Start-Process -FilePath $installerPath -ArgumentList "--quiet", "--wait", "--add", "Microsoft.VisualStudio.Workload.ManagedGame" -Wait
    
    Remove-Item $installerPath -Force
    Write-ColorOutput $Green "Visual Studio Community 2022 installed successfully!"
}

function Install-UnityHub {
    if ($SkipUnityHub) {
        Write-ColorOutput $Yellow "Skipping Unity Hub installation..."
        return
    }
    
    Write-ColorOutput $Blue "Installing Unity Hub..."
    
    # Check if Unity Hub is already installed
    $hubPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    if (Test-Path $hubPath) {
        Write-ColorOutput $Green "Unity Hub is already installed."
        return $hubPath
    }
    
    # Download Unity Hub
    $hubUrl = "https://public-cdn.cloud.unity3d.com/hub/prod/UnityHubSetup.exe"
    $hubInstaller = "$env:TEMP\UnityHubSetup.exe"
    
    Write-ColorOutput $Blue "Downloading Unity Hub..."
    Invoke-WebRequest -Uri $hubUrl -OutFile $hubInstaller
    
    # Install Unity Hub
    Write-ColorOutput $Blue "Installing Unity Hub..."
    Start-Process -FilePath $hubInstaller -ArgumentList "/S" -Wait
    
    Remove-Item $hubInstaller -Force
    Write-ColorOutput $Green "Unity Hub installed successfully!"
    
    return $hubPath
}

function Install-UnityEditor {
    param($HubPath, $Version)
    
    Write-ColorOutput $Blue "Installing Unity Editor $Version..."
    
    # Check if Unity version is already installed
    $unityPath = "${env:ProgramFiles}\Unity\Hub\Editor\$Version\Editor\Unity.exe"
    if (Test-Path $unityPath) {
        Write-ColorOutput $Green "Unity $Version is already installed."
        return $unityPath
    }
    
    # Install Unity Editor using Unity Hub CLI
    $hubCliPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    
    if (Test-Path $hubCliPath) {
        Write-ColorOutput $Blue "Installing Unity $Version with modules..."
        
        # Install Unity with common modules
        $modules = @(
            "android",           # Android Build Support
            "ios",              # iOS Build Support  
            "windows-il2cpp",   # Windows Build Support (IL2CPP)
            "mac-mono",         # Mac Build Support (Mono)
            "linux-il2cpp",     # Linux Build Support (IL2CPP)
            "webgl"             # WebGL Build Support
        )
        
        $moduleArgs = $modules -join ","
        
        # Use Unity Hub CLI to install
        Start-Process -FilePath $hubCliPath -ArgumentList "-- --headless install --version $Version --module $moduleArgs" -Wait
        
        Write-ColorOutput $Green "Unity $Version installed successfully!"
        return $unityPath
    } else {
        Write-ColorOutput $Red "Unity Hub CLI not found. Please install Unity manually through Unity Hub."
        return $null
    }
}

function Install-AndroidSDK {
    if ($SkipAndroidSDK) {
        Write-ColorOutput $Yellow "Skipping Android SDK installation..."
        return
    }
    
    Write-ColorOutput $Blue "Installing Android SDK..."
    
    # Check if Android SDK is already installed
    $androidHome = $env:ANDROID_HOME
    if ($androidHome -and (Test-Path "$androidHome\platform-tools\adb.exe")) {
        Write-ColorOutput $Green "Android SDK is already installed."
        return
    }
    
    # Install Android SDK via Chocolatey
    choco install android-sdk -y
    
    # Set environment variables
    $androidSdkPath = "${env:ProgramFiles(x86)}\Android\android-sdk"
    if (Test-Path $androidSdkPath) {
        [Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidSdkPath, "Machine")
        [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $androidSdkPath, "Machine")
        
        $currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
        $newPath = "$currentPath;$androidSdkPath\platform-tools;$androidSdkPath\tools"
        [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
        
        Write-ColorOutput $Green "Android SDK installed and configured successfully!"
    }
}

function Install-NodeJS {
    Write-ColorOutput $Blue "Installing Node.js..."
    
    if (Get-Command node -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "Node.js is already installed."
        return
    }
    
    choco install nodejs -y
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Node.js installed successfully!"
}

function Create-UnityProjectStructure {
    Write-ColorOutput $Blue "Setting up Unity project structure..."
    
    $projectPath = Get-Location
    
    # Create necessary directories
    $directories = @(
        "Assets\Editor",
        "Assets\Scripts",
        "Assets\Prefabs",
        "Assets\Scenes",
        "Assets\Materials",
        "Assets\Textures",
        "Assets\Audio",
        "Builds"
    )
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $projectPath $dir
        if (!(Test-Path $fullPath)) {
            New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
            Write-ColorOutput $Green "Created directory: $dir"
        }
    }
    
    Write-ColorOutput $Green "Unity project structure created successfully!"
}

function Show-InstallationSummary {
    param($UnityPath)
    
    Write-ColorOutput $Blue "`n========================================="
    Write-ColorOutput $Blue "    INSTALLATION SUMMARY"
    Write-ColorOutput $Blue "========================================="
    
    Write-ColorOutput $Green "`nInstalled Components:"
    
    if (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "✓ Chocolatey Package Manager"
    }
    
    if (Get-Command git -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "✓ Git Version Control"
    }
    
    $vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe"
    if (Test-Path $vsPath) {
        Write-ColorOutput $Green "✓ Visual Studio Community 2022"
    }
    
    $hubPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    if (Test-Path $hubPath) {
        Write-ColorOutput $Green "✓ Unity Hub"
    }
    
    if ($UnityPath -and (Test-Path $UnityPath)) {
        Write-ColorOutput $Green "✓ Unity Editor $UnityVersion"
    }
    
    if (Get-Command node -ErrorAction SilentlyContinue) {
        Write-ColorOutput $Green "✓ Node.js"
    }
    
    if ($env:ANDROID_HOME) {
        Write-ColorOutput $Green "✓ Android SDK"
    }
    
    Write-ColorOutput $Yellow "`nNext Steps:"
    Write-ColorOutput $Yellow "1. Restart your computer to ensure all environment variables are loaded"
    Write-ColorOutput $Yellow "2. Open Unity Hub and sign in with your Unity account"
    Write-ColorOutput $Yellow "3. Create a new Unity project or open an existing one"
    Write-ColorOutput $Yellow "4. Run the build system setup: Build Tools > Setup Build System"
    
    Write-ColorOutput $Blue "`n========================================="
}

# Main installation process
function Main {
    Write-ColorOutput $Blue "========================================="
    Write-ColorOutput $Blue "  Unity Development Environment Setup"
    Write-ColorOutput $Blue "========================================="
    
    # Check if running as administrator
    if (!(Test-Administrator)) {
        Write-ColorOutput $Red "This script must be run as Administrator!"
        Write-ColorOutput $Yellow "Please right-click PowerShell and select 'Run as Administrator'"
        exit 1
    }
    
    Write-ColorOutput $Green "Starting Unity development environment setup..."
    Write-ColorOutput $Yellow "This may take 30-60 minutes depending on your internet connection."
    
    $confirmation = Read-Host "`nDo you want to continue? (y/n)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-ColorOutput $Yellow "Installation cancelled."
        exit 0
    }
    
    try {
        # Install components
        Install-Chocolatey
        Install-Git
        Install-NodeJS
        Install-VisualStudio
        $hubPath = Install-UnityHub
        $unityPath = Install-UnityEditor -HubPath $hubPath -Version $UnityVersion
        Install-AndroidSDK
        Create-UnityProjectStructure
        
        Show-InstallationSummary -UnityPath $unityPath
        
        Write-ColorOutput $Green "`nInstallation completed successfully!"
        Write-ColorOutput $Yellow "Please restart your computer before using Unity."
        
    } catch {
        Write-ColorOutput $Red "An error occurred during installation: $($_.Exception.Message)"
        Write-ColorOutput $Yellow "Please check the error and try again."
        exit 1
    }
}

# Run main function
Main
