using System.IO;
using UnityEditor;
using UnityEngine;

namespace BuildTools
{
    public class BuildSystemSetup : EditorWindow
    {
        private string unityPath = "";
        private string projectName = "";
        private string companyName = "";
        private string initialVersion = "1.0.0";
        private bool setupComplete = false;

        [MenuItem("Build Tools/Setup Build System")]
        public static void ShowSetupWindow()
        {
            var window = GetWindow<BuildSystemSetup>("Build System Setup");
            window.minSize = new Vector2(500, 400);
            window.Show();
        }

        private void OnEnable()
        {
            // Initialize with current project settings
            projectName = PlayerSettings.productName;
            companyName = PlayerSettings.companyName;
            
            // Try to detect Unity path
            DetectUnityPath();
            
            // Check if setup is already complete
            CheckSetupStatus();
        }

        private void OnGUI()
        {
            GUILayout.Label("Unity Multi-Platform Build System Setup", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            if (setupComplete)
            {
                EditorGUILayout.HelpBox("Build system is already configured!", MessageType.Info);
                EditorGUILayout.Space();
                
                if (GUILayout.Button("Reconfigure"))
                {
                    setupComplete = false;
                }
                
                if (GUILayout.Button("Open Build Window"))
                {
                    MultiPlatformBuilder.ShowWindow();
                }
                
                return;
            }

            EditorGUILayout.LabelField("Project Configuration", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            projectName = EditorGUILayout.TextField("Project Name:", projectName);
            companyName = EditorGUILayout.TextField("Company Name:", companyName);
            initialVersion = EditorGUILayout.TextField("Initial Version:", initialVersion);
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Unity Path Configuration", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("Current Unity Path:");
            EditorGUILayout.SelectableLabel(unityPath, EditorStyles.textField, GUILayout.Height(EditorGUIUtility.singleLineHeight));
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Auto Detect"))
            {
                DetectUnityPath();
            }
            if (GUILayout.Button("Browse"))
            {
                BrowseForUnityPath();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Setup checklist
            EditorGUILayout.LabelField("Setup Checklist", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            bool hasNewtonsoft = HasNewtonsoftJson();
            bool hasValidUnityPath = !string.IsNullOrEmpty(unityPath) && (File.Exists(unityPath) || Directory.Exists(unityPath));
            bool hasValidProjectInfo = !string.IsNullOrEmpty(projectName) && !string.IsNullOrEmpty(companyName);
            
            DrawChecklistItem("Newtonsoft.Json Package", hasNewtonsoft);
            DrawChecklistItem("Valid Unity Path", hasValidUnityPath);
            DrawChecklistItem("Project Information", hasValidProjectInfo);
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Actions
            EditorGUILayout.BeginHorizontal();
            
            if (!hasNewtonsoft)
            {
                if (GUILayout.Button("Install Newtonsoft.Json"))
                {
                    InstallNewtonsoftJson();
                }
            }
            
            bool canSetup = hasNewtonsoft && hasValidUnityPath && hasValidProjectInfo;
            
            GUI.enabled = canSetup;
            if (GUILayout.Button("Complete Setup"))
            {
                CompleteSetup();
            }
            GUI.enabled = true;
            
            EditorGUILayout.EndHorizontal();

            if (!canSetup)
            {
                EditorGUILayout.HelpBox("Please complete all checklist items before proceeding.", MessageType.Warning);
            }
        }

        private void DrawChecklistItem(string label, bool isComplete)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(isComplete ? "✓" : "✗", GUILayout.Width(20));
            EditorGUILayout.LabelField(label);
            EditorGUILayout.EndHorizontal();
        }

        private void DetectUnityPath()
        {
            string editorPath = EditorApplication.applicationPath;
            
            #if UNITY_EDITOR_WIN
            unityPath = editorPath;
            #elif UNITY_EDITOR_OSX
            unityPath = editorPath;
            #elif UNITY_EDITOR_LINUX
            unityPath = editorPath;
            #endif
            
            Debug.Log($"Detected Unity path: {unityPath}");
        }

        private void BrowseForUnityPath()
        {
            #if UNITY_EDITOR_WIN
            string path = EditorUtility.OpenFilePanel("Select Unity.exe", "", "exe");
            #elif UNITY_EDITOR_OSX
            string path = EditorUtility.OpenFilePanel("Select Unity", "/Applications", "");
            #else
            string path = EditorUtility.OpenFilePanel("Select Unity", "", "");
            #endif
            
            if (!string.IsNullOrEmpty(path))
            {
                unityPath = path;
            }
        }

        private bool HasNewtonsoftJson()
        {
            return System.Type.GetType("Newtonsoft.Json.JsonConvert, Newtonsoft.Json") != null;
        }

        private void InstallNewtonsoftJson()
        {
            UnityEditor.PackageManager.Client.Add("com.unity.nuget.newtonsoft-json");
            EditorUtility.DisplayDialog("Package Installation", 
                "Newtonsoft.Json package installation started. Please wait for it to complete and then reopen this window.", 
                "OK");
        }

        private void CheckSetupStatus()
        {
            string versionPath = Path.Combine(Application.dataPath, "../version.json");
            string buildBatPath = Path.Combine(Application.dataPath, "../build.bat");
            string buildShPath = Path.Combine(Application.dataPath, "../build.sh");
            
            setupComplete = File.Exists(versionPath) && (File.Exists(buildBatPath) || File.Exists(buildShPath));
        }

        private void CompleteSetup()
        {
            try
            {
                // Update PlayerSettings
                PlayerSettings.productName = projectName;
                PlayerSettings.companyName = companyName;
                
                // Create or update version.json
                CreateVersionFile();
                
                // Update build scripts with Unity path
                UpdateBuildScripts();
                
                // Create build configuration asset
                CreateBuildConfiguration();
                
                setupComplete = true;
                
                EditorUtility.DisplayDialog("Setup Complete", 
                    "Build system setup completed successfully!\n\n" +
                    "You can now use:\n" +
                    "• Build Tools menu in Unity\n" +
                    "• build.bat (Windows) or build.sh (Mac/Linux)\n" +
                    "• Command line builds for CI/CD", 
                    "OK");
                
                // Open build window
                MultiPlatformBuilder.ShowWindow();
            }
            catch (System.Exception e)
            {
                EditorUtility.DisplayDialog("Setup Error", 
                    $"An error occurred during setup:\n{e.Message}", 
                    "OK");
            }
        }

        private void CreateVersionFile()
        {
            string versionPath = Path.Combine(Application.dataPath, "../version.json");
            
            if (!File.Exists(versionPath))
            {
                var versionInfo = new VersionInfo
                {
                    version = initialVersion,
                    buildTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    changelog = $"Initial release of {projectName}",
                    buildNumber = 1
                };
                
                string json = JsonUtility.ToJson(versionInfo, true);
                File.WriteAllText(versionPath, json);
                
                Debug.Log($"Created version.json at: {versionPath}");
            }
        }

        private void UpdateBuildScripts()
        {
            // Update build.bat
            string buildBatPath = Path.Combine(Application.dataPath, "../build.bat");
            if (File.Exists(buildBatPath))
            {
                string content = File.ReadAllText(buildBatPath);
                content = content.Replace("set UNITY_PATH=\"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.8f1\\Editor\\Unity.exe\"", 
                                        $"set UNITY_PATH=\"{unityPath}\"");
                File.WriteAllText(buildBatPath, content);
            }
            
            // Update build.sh
            string buildShPath = Path.Combine(Application.dataPath, "../build.sh");
            if (File.Exists(buildShPath))
            {
                string content = File.ReadAllText(buildShPath);
                content = content.Replace("UNITY_PATH=\"/Applications/Unity/Hub/Editor/2022.3.8f1/Unity.app/Contents/MacOS/Unity\"", 
                                        $"UNITY_PATH=\"{unityPath}\"");
                File.WriteAllText(buildShPath, content);
            }
        }

        private void CreateBuildConfiguration()
        {
            string configPath = "Assets/BuildConfiguration.asset";
            
            if (!File.Exists(configPath))
            {
                var config = CreateInstance<BuildConfiguration>();
                config.productName = projectName;
                config.companyName = companyName;
                
                AssetDatabase.CreateAsset(config, configPath);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"Created build configuration at: {configPath}");
            }
        }
    }
}
