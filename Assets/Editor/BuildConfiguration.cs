using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace BuildTools
{
    [Serializable]
    public class PlatformBuildConfig
    {
        public string platformName;
        public BuildTarget buildTarget;
        public string fileExtension;
        public bool enabled = true;
        public BuildOptions buildOptions = BuildOptions.None;
        public string customDefines = "";
        
        public PlatformBuildConfig(string name, BuildTarget target, string extension)
        {
            platformName = name;
            buildTarget = target;
            fileExtension = extension;
        }
    }

    [CreateAssetMenu(fileName = "BuildConfiguration", menuName = "Build Tools/Build Configuration")]
    public class BuildConfiguration : ScriptableObject
    {
        [Header("General Settings")]
        public string productName = "PerfumerProject";
        public string companyName = "YourCompany";
        public bool developmentBuild = false;
        public bool autoConnectProfiler = false;
        public bool scriptDebugging = false;
        
        [Header("Version Settings")]
        public bool autoIncrementVersion = true;
        public bool useGitCommitCount = false;
        
        [Header("Build Output")]
        public string buildOutputPath = "Builds";
        public bool createVersionFolder = true;
        public bool zipBuilds = false;
        
        [Header("Platform Configurations")]
        public List<PlatformBuildConfig> platformConfigs = new List<PlatformBuildConfig>();
        
        [Header("Pre/Post Build Commands")]
        [TextArea(3, 5)]
        public string preBuildCommands = "";
        [TextArea(3, 5)]
        public string postBuildCommands = "";
        
        private void OnEnable()
        {
            if (platformConfigs == null || platformConfigs.Count == 0)
            {
                InitializeDefaultConfigs();
            }
        }
        
        private void InitializeDefaultConfigs()
        {
            platformConfigs = new List<PlatformBuildConfig>
            {
                new PlatformBuildConfig("Windows", BuildTarget.StandaloneWindows64, ".exe"),
                new PlatformBuildConfig("Mac", BuildTarget.StandaloneOSX, ".app"),
                new PlatformBuildConfig("Linux", BuildTarget.StandaloneLinux64, ""),
                new PlatformBuildConfig("Android", BuildTarget.Android, ".apk"),
                new PlatformBuildConfig("iOS", BuildTarget.iOS, "")
            };
            
            // Set platform-specific options
            var androidConfig = platformConfigs.Find(c => c.buildTarget == BuildTarget.Android);
            if (androidConfig != null)
            {
                androidConfig.buildOptions = BuildOptions.None;
            }
            
            var iosConfig = platformConfigs.Find(c => c.buildTarget == BuildTarget.iOS);
            if (iosConfig != null)
            {
                iosConfig.buildOptions = BuildOptions.None;
            }
        }
        
        public PlatformBuildConfig GetPlatformConfig(BuildTarget target)
        {
            return platformConfigs.Find(config => config.buildTarget == target);
        }
        
        public List<PlatformBuildConfig> GetEnabledPlatforms()
        {
            return platformConfigs.FindAll(config => config.enabled);
        }
    }

    [CustomEditor(typeof(BuildConfiguration))]
    public class BuildConfigurationEditor : Editor
    {
        private SerializedProperty platformConfigsProp;
        
        private void OnEnable()
        {
            platformConfigsProp = serializedObject.FindProperty("platformConfigs");
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            DrawDefaultInspector();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Quick Actions", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Enable All Platforms"))
            {
                var config = target as BuildConfiguration;
                foreach (var platform in config.platformConfigs)
                {
                    platform.enabled = true;
                }
                EditorUtility.SetDirty(target);
            }
            
            if (GUILayout.Button("Disable All Platforms"))
            {
                var config = target as BuildConfiguration;
                foreach (var platform in config.platformConfigs)
                {
                    platform.enabled = false;
                }
                EditorUtility.SetDirty(target);
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Reset to Defaults"))
            {
                var config = target as BuildConfiguration;
                config.platformConfigs.Clear();
                var method = typeof(BuildConfiguration).GetMethod("InitializeDefaultConfigs", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                method?.Invoke(config, null);
                EditorUtility.SetDirty(target);
            }
            
            if (GUILayout.Button("Open Build Window"))
            {
                MultiPlatformBuilder.ShowWindow();
            }
            
            EditorGUILayout.EndHorizontal();
            
            serializedObject.ApplyModifiedProperties();
        }
    }
}
