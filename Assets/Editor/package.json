{"name": "com.perfumerproject.buildtools", "displayName": "Unity Multi-Platform Build Tools", "version": "1.0.0", "description": "A comprehensive Unity build system supporting multi-platform builds with version management and Git integration.", "unity": "2022.3", "keywords": ["build", "automation", "ci-cd", "multi-platform", "version-management", "git-integration"], "author": {"name": "PerfumerProject Team", "email": "<EMAIL>"}, "dependencies": {"com.unity.nuget.newtonsoft-json": "3.0.2"}, "samples": [{"displayName": "Build Configuration Sample", "description": "Sample build configuration asset", "path": "Samples~/BuildConfigSample"}]}