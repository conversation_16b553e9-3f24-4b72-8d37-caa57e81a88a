using System;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using Newtonsoft.Json;

namespace BuildTools
{
    [Serializable]
    public class VersionInfo
    {
        public string version = "1.0.0";
        public string buildTime = "";
        public string changelog = "";
        public int buildNumber = 1;
    }

    public class MultiPlatformBuilder : EditorWindow
    {
        private static readonly string VERSION_FILE_PATH = Path.Combine(Application.dataPath, "../version.json");
        private static readonly string BUILD_OUTPUT_PATH = Path.Combine(Application.dataPath, "../Builds");
        
        private VersionInfo currentVersion;
        private Vector2 scrollPosition;
        private bool[] platformToggles = new bool[5]; // iOS, Android, Windows, Mac, Linux
        private string[] platformNames = { "iOS", "Android", "Windows", "Mac", "Linux" };
        private BuildTarget[] buildTargets = { 
            BuildTarget.iOS, 
            BuildTarget.Android, 
            BuildTarget.StandaloneWindows64, 
            BuildTarget.StandaloneOSX, 
            BuildTarget.StandaloneLinux64 
        };

        [MenuItem("Build Tools/Multi-Platform Builder")]
        public static void ShowWindow()
        {
            GetWindow<MultiPlatformBuilder>("Multi-Platform Builder");
        }

        [MenuItem("Build Tools/Build All Platforms")]
        public static void BuildAllPlatforms()
        {
            var builder = new MultiPlatformBuilder();
            builder.LoadVersionInfo();
            builder.UpdateVersionFromGit();
            
            for (int i = 0; i < builder.buildTargets.Length; i++)
            {
                builder.BuildForPlatform(builder.buildTargets[i], builder.platformNames[i]);
            }
            
            UnityEngine.Debug.Log("All platforms build completed!");
        }

        // Individual platform build menu items
        [MenuItem("Build Tools/Build iOS")]
        public static void BuildiOS() => BuildSinglePlatform(BuildTarget.iOS, "iOS");

        [MenuItem("Build Tools/Build Android")]
        public static void BuildAndroid() => BuildSinglePlatform(BuildTarget.Android, "Android");

        [MenuItem("Build Tools/Build Windows")]
        public static void BuildWindows() => BuildSinglePlatform(BuildTarget.StandaloneWindows64, "Windows");

        [MenuItem("Build Tools/Build Mac")]
        public static void BuildMac() => BuildSinglePlatform(BuildTarget.StandaloneOSX, "Mac");

        [MenuItem("Build Tools/Build Linux")]
        public static void BuildLinux() => BuildSinglePlatform(BuildTarget.StandaloneLinux64, "Linux");

        private static void BuildSinglePlatform(BuildTarget target, string platformName)
        {
            var builder = new MultiPlatformBuilder();
            builder.LoadVersionInfo();
            builder.UpdateVersionFromGit();
            builder.BuildForPlatform(target, platformName);
            UnityEngine.Debug.Log($"{platformName} build completed!");
        }

        private void OnEnable()
        {
            LoadVersionInfo();
        }

        private void OnGUI()
        {
            GUILayout.Label("Multi-Platform Builder", EditorStyles.boldLabel);
            
            EditorGUILayout.Space();
            
            // Version Info Section
            GUILayout.Label("Version Information", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            currentVersion.version = EditorGUILayout.TextField("Version:", currentVersion.version);
            EditorGUILayout.LabelField("Build Number:", currentVersion.buildNumber.ToString());
            EditorGUILayout.LabelField("Last Build Time:", currentVersion.buildTime);
            
            EditorGUILayout.Space();
            GUILayout.Label("Changelog:", EditorStyles.boldLabel);
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(100));
            currentVersion.changelog = EditorGUILayout.TextArea(currentVersion.changelog, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space();
            
            // Platform Selection
            GUILayout.Label("Platform Selection", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            for (int i = 0; i < platformNames.Length; i++)
            {
                platformToggles[i] = EditorGUILayout.Toggle(platformNames[i], platformToggles[i]);
            }
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space();
            
            // Buttons
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Update Version from Git"))
            {
                UpdateVersionFromGit();
            }
            
            if (GUILayout.Button("Save Version Info"))
            {
                SaveVersionInfo();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Build Selected Platforms"))
            {
                BuildSelectedPlatforms();
            }
            
            if (GUILayout.Button("Build All Platforms"))
            {
                BuildAllSelectedPlatforms();
            }
            
            EditorGUILayout.EndHorizontal();
        }

        private void LoadVersionInfo()
        {
            if (File.Exists(VERSION_FILE_PATH))
            {
                try
                {
                    string json = File.ReadAllText(VERSION_FILE_PATH);
                    currentVersion = JsonConvert.DeserializeObject<VersionInfo>(json);
                }
                catch (Exception e)
                {
                    UnityEngine.Debug.LogError($"Failed to load version info: {e.Message}");
                    currentVersion = new VersionInfo();
                }
            }
            else
            {
                currentVersion = new VersionInfo();
                SaveVersionInfo();
            }
        }

        private void SaveVersionInfo()
        {
            try
            {
                currentVersion.buildTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                string json = JsonConvert.SerializeObject(currentVersion, Formatting.Indented);
                File.WriteAllText(VERSION_FILE_PATH, json);
                UnityEngine.Debug.Log($"Version info saved to: {VERSION_FILE_PATH}");
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError($"Failed to save version info: {e.Message}");
            }
        }

        private void UpdateVersionFromGit()
        {
            try
            {
                string lastBuildTime = currentVersion.buildTime;
                if (string.IsNullOrEmpty(lastBuildTime))
                {
                    lastBuildTime = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd HH:mm:ss");
                }

                string gitLog = GetGitCommitsSince(lastBuildTime);
                if (!string.IsNullOrEmpty(gitLog))
                {
                    currentVersion.changelog = GenerateChangelogFromGitLog(gitLog);
                    currentVersion.buildNumber++;
                }
                
                Repaint();
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError($"Failed to update version from Git: {e.Message}");
            }
        }

        private string GetGitCommitsSince(string sinceDate)
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = "git",
                    Arguments = $"log --since=\"{sinceDate}\" --pretty=format:\"%h - %an, %ar : %s\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true,
                    WorkingDirectory = Application.dataPath + "/.."
                };

                using (Process process = Process.Start(startInfo))
                {
                    using (var reader = process.StandardOutput)
                    {
                        return reader.ReadToEnd();
                    }
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogWarning($"Git command failed: {e.Message}");
                return "";
            }
        }

        private string GenerateChangelogFromGitLog(string gitLog)
        {
            if (string.IsNullOrEmpty(gitLog))
                return "No recent changes found.";

            var lines = gitLog.Split('\n');
            var changelog = $"Version {currentVersion.version} Build {currentVersion.buildNumber + 1}\n";
            changelog += $"Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n";
            changelog += "Recent Changes:\n";
            
            foreach (var line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    changelog += $"• {line.Trim()}\n";
                }
            }
            
            return changelog;
        }

        private void BuildSelectedPlatforms()
        {
            UpdateVersionFromGit();
            
            for (int i = 0; i < platformToggles.Length; i++)
            {
                if (platformToggles[i])
                {
                    BuildForPlatform(buildTargets[i], platformNames[i]);
                }
            }
            
            SaveVersionInfo();
        }

        private void BuildAllSelectedPlatforms()
        {
            for (int i = 0; i < platformToggles.Length; i++)
            {
                platformToggles[i] = true;
            }
            BuildSelectedPlatforms();
        }

        private void BuildForPlatform(BuildTarget target, string platformName)
        {
            try
            {
                UnityEngine.Debug.Log($"Starting build for {platformName}...");
                
                // Create build directory
                string buildPath = Path.Combine(BUILD_OUTPUT_PATH, platformName);
                Directory.CreateDirectory(buildPath);
                
                // Set build options
                BuildPlayerOptions buildOptions = new BuildPlayerOptions
                {
                    scenes = GetEnabledScenes(),
                    locationPathName = GetBuildPath(buildPath, target, platformName),
                    target = target,
                    options = BuildOptions.None
                };
                
                // Update player settings
                UpdatePlayerSettings();
                
                // Build
                var report = BuildPipeline.BuildPlayer(buildOptions);
                
                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    UnityEngine.Debug.Log($"{platformName} build succeeded: {buildOptions.locationPathName}");
                }
                else
                {
                    UnityEngine.Debug.LogError($"{platformName} build failed!");
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError($"Build failed for {platformName}: {e.Message}");
            }
        }

        private string[] GetEnabledScenes()
        {
            var scenes = new List<string>();
            foreach (var scene in EditorBuildSettings.scenes)
            {
                if (scene.enabled)
                {
                    scenes.Add(scene.path);
                }
            }
            return scenes.ToArray();
        }

        private string GetBuildPath(string buildPath, BuildTarget target, string platformName)
        {
            string fileName = PlayerSettings.productName;
            
            switch (target)
            {
                case BuildTarget.StandaloneWindows64:
                    return Path.Combine(buildPath, $"{fileName}.exe");
                case BuildTarget.StandaloneOSX:
                    return Path.Combine(buildPath, $"{fileName}.app");
                case BuildTarget.StandaloneLinux64:
                    return Path.Combine(buildPath, fileName);
                case BuildTarget.Android:
                    return Path.Combine(buildPath, $"{fileName}.apk");
                case BuildTarget.iOS:
                    return buildPath; // iOS builds to a folder
                default:
                    return Path.Combine(buildPath, fileName);
            }
        }

        private void UpdatePlayerSettings()
        {
            PlayerSettings.bundleVersion = currentVersion.version;
            
            // Update build number for different platforms
            PlayerSettings.Android.bundleVersionCode = currentVersion.buildNumber;
            PlayerSettings.iOS.buildNumber = currentVersion.buildNumber.ToString();
            PlayerSettings.macOS.buildNumber = currentVersion.buildNumber.ToString();
        }
    }
}
