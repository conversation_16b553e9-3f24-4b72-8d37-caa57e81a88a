# Unity Multi-Platform Build System

这是一个完整的Unity多平台构建系统，支持一键导出iOS、Android、Windows、Mac、Linux版本，并集成了版本管理和Git提交记录自动生成功能。

## 🚀 功能特性

- ✅ **多平台支持**: iOS, Android, Windows, Mac, Linux
- ✅ **一键构建**: 支持全平台或单平台构建
- ✅ **版本管理**: 自动读取和更新version.json
- ✅ **Git集成**: 自动生成基于Git提交记录的版本说明
- ✅ **菜单集成**: Unity编辑器菜单快速访问
- ✅ **命令行支持**: 支持CI/CD集成
- ✅ **配置化**: 可自定义构建参数和平台设置
- ✅ **日志记录**: 详细的构建日志和错误报告

## 📁 文件结构

```
PerfumerProject/
├── Assets/
│   └── Editor/
│       ├── MultiPlatformBuilder.cs      # 主构建器窗口
│       ├── BuildConfiguration.cs        # 构建配置管理
│       └── CommandLineBuild.cs          # 命令行构建支持
├── version.json                         # 版本信息文件
├── build.bat                           # Windows批处理脚本
├── build.sh                            # Mac/Linux Shell脚本
└── BUILD_SYSTEM_README.md              # 本文档
```

## 🛠️ 安装和设置

### 1. 导入脚本文件
将所有Editor脚本放入 `Assets/Editor/` 目录下。

### 2. 安装依赖
确保项目中已安装以下包：
- Newtonsoft.Json (用于JSON序列化)

如果没有安装，可以通过Package Manager添加：
```
Window > Package Manager > + > Add package by name
com.unity.nuget.newtonsoft-json
```

### 3. 配置Unity路径
编辑 `build.bat` 或 `build.sh` 文件，更新Unity安装路径：

**Windows (build.bat):**
```batch
set UNITY_PATH="C:\Program Files\Unity\Hub\Editor\2022.3.8f1\Editor\Unity.exe"
```

**Mac/Linux (build.sh):**
```bash
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.8f1/Unity.app/Contents/MacOS/Unity"
```

## 📋 使用方法

### 方法1: Unity编辑器内使用

#### 打开构建窗口
- 菜单: `Build Tools > Multi-Platform Builder`

#### 快速构建菜单
- `Build Tools > Build All Platforms` - 构建所有平台
- `Build Tools > Build iOS` - 仅构建iOS
- `Build Tools > Build Android` - 仅构建Android
- `Build Tools > Build Windows` - 仅构建Windows
- `Build Tools > Build Mac` - 仅构建Mac
- `Build Tools > Build Linux` - 仅构建Linux

#### 构建窗口功能
1. **版本信息管理**
   - 编辑版本号
   - 查看构建编号和时间
   - 编辑版本说明

2. **平台选择**
   - 勾选要构建的平台
   - 支持多选或全选

3. **Git集成**
   - 点击"Update Version from Git"自动生成版本说明
   - 基于上次构建时间获取Git提交记录

### 方法2: 命令行使用

#### Windows
```batch
# 构建所有平台
build.bat

# 构建特定平台
build.bat -platform windows
build.bat -platform android -version 1.0.1

# 开发版本构建
build.bat -platform all -development
```

#### Mac/Linux
```bash
# 给脚本添加执行权限
chmod +x build.sh

# 构建所有平台
./build.sh

# 构建特定平台
./build.sh -p windows
./build.sh -p android -v 1.0.1

# 开发版本构建
./build.sh --platform all --development
```

### 方法3: Unity命令行直接调用

```bash
# 构建所有平台
Unity -batchmode -quit -projectPath "path/to/project" -executeMethod BuildTools.CommandLineBuild.BuildAll

# 构建特定平台
Unity -batchmode -quit -projectPath "path/to/project" -executeMethod BuildTools.CommandLineBuild.Build -platform windows -version 1.0.1
```

## ⚙️ 配置说明

### version.json 文件格式
```json
{
  "version": "1.0.0",
  "buildTime": "2024-06-24 10:00:00",
  "changelog": "版本更新说明...",
  "buildNumber": 1
}
```

### 构建配置 (BuildConfiguration)
可以创建ScriptableObject配置文件来自定义构建参数：

1. 右键 `Create > Build Tools > Build Configuration`
2. 配置各平台的构建选项
3. 设置输出路径和构建参数

## 🔧 高级功能

### Git集成原理
系统会执行以下Git命令获取提交记录：
```bash
git log --since="上次构建时间" --pretty=format:"%h - %an, %ar : %s"
```

### 自动版本号管理
- 每次构建自动递增buildNumber
- 支持手动设置version版本号
- 自动更新PlayerSettings中的版本信息

### 平台特定设置
- **Android**: 自动更新bundleVersionCode
- **iOS**: 自动更新buildNumber
- **Mac**: 自动更新buildNumber
- **Windows/Linux**: 使用通用版本号

## 🚨 故障排除

### 常见问题

1. **Unity路径错误**
   - 检查build.bat或build.sh中的UNITY_PATH设置
   - 确保路径指向正确的Unity版本

2. **Git命令失败**
   - 确保项目在Git仓库中
   - 检查Git是否在系统PATH中

3. **JSON序列化错误**
   - 确保安装了Newtonsoft.Json包
   - 检查version.json文件格式是否正确

4. **构建失败**
   - 检查Build Settings中是否添加了场景
   - 确保目标平台已安装相应模块

### 日志文件
构建日志保存在项目根目录的 `build.log` 文件中，可以查看详细的构建信息和错误。

## 🔄 CI/CD 集成

### GitHub Actions 示例
```yaml
name: Build Unity Project

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Build Unity Project
      run: |
        Unity -batchmode -quit -projectPath . \
        -executeMethod BuildTools.CommandLineBuild.BuildAll \
        -version ${{ github.ref_name }}
```

### Jenkins 示例
```groovy
pipeline {
    agent any
    
    stages {
        stage('Build') {
            steps {
                script {
                    sh './build.sh -p all -v ${BUILD_NUMBER}'
                }
            }
        }
    }
}
```

## 📝 自定义扩展

### 添加新平台
1. 在 `MultiPlatformBuilder.cs` 中添加新的BuildTarget
2. 更新 `buildTargets` 和 `platformNames` 数组
3. 在 `CommandLineBuild.cs` 中添加对应的映射

### 自定义构建后处理
可以在 `BuildConfiguration.cs` 中添加构建前后的命令执行：
- `preBuildCommands`: 构建前执行的命令
- `postBuildCommands`: 构建后执行的命令

## 📄 许可证

本构建系统基于MIT许可证开源，可自由使用和修改。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个构建系统！
